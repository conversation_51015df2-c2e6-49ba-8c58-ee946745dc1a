# simulateKeyboard Action Usage Examples

The `simulateKeyboard` action allows you to simulate keyboard input on elements in your Chrome extension. This is useful for automating form interactions, navigation, and other keyboard-driven actions.

## Basic Usage

### Simple Key Press

```typescript
import { ElementActions } from '@/services/element-query/element-actions';

const actions = new ElementActions();
const element = document.querySelector('#my-input');

// Simulate pressing the 'a' key
const action = {
  type: 'simulateKeyboard',
  params: {
    key: 'a'
  }
};

await actions.executeAction(element, action);
```

### Special Keys

```typescript
// Press Enter key
const enterAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'Enter'
  }
};

// Press Tab key
const tabAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'Tab'
  }
};

// Press Escape key
const escapeAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'Escape'
  }
};
```

### Arrow Keys

```typescript
// Navigate with arrow keys
const arrowActions = [
  { type: 'simulateKeyboard', params: { key: 'ArrowUp' } },
  { type: 'simulateKeyboard', params: { key: 'ArrowDown' } },
  { type: 'simulateKeyboard', params: { key: 'ArrowLeft' } },
  { type: 'simulateKeyboard', params: { key: 'ArrowRight' } }
];
```

## Modifier Keys

### Ctrl Combinations

```typescript
// Ctrl+A (Select All)
const selectAllAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'a',
    options: {
      ctrlKey: true
    }
  }
};

// Ctrl+C (Copy)
const copyAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'c',
    options: {
      ctrlKey: true
    }
  }
};

// Ctrl+V (Paste)
const pasteAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'v',
    options: {
      ctrlKey: true
    }
  }
};
```

### Shift Combinations

```typescript
// Shift+Tab (Reverse tab)
const shiftTabAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'Tab',
    options: {
      shiftKey: true
    }
  }
};

// Shift+Arrow (Select text)
const selectTextAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'ArrowRight',
    options: {
      shiftKey: true
    }
  }
};
```

### Alt and Meta Keys

```typescript
// Alt+F4 (Close window)
const altF4Action = {
  type: 'simulateKeyboard',
  params: {
    key: 'F4',
    options: {
      altKey: true
    }
  }
};

// Cmd+Z (Undo on Mac)
const undoAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'z',
    options: {
      metaKey: true  // Command key on Mac
    }
  }
};
```

## Function Keys

```typescript
// Function keys F1-F12
const functionKeyActions = [
  { type: 'simulateKeyboard', params: { key: 'F1' } },
  { type: 'simulateKeyboard', params: { key: 'F5' } },  // Refresh
  { type: 'simulateKeyboard', params: { key: 'F12' } }  // Developer tools
];
```

## Advanced Usage

### With Delay

```typescript
// Add delay before executing the keyboard action
const delayedAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'Enter',
    delay: 500  // Wait 500ms before pressing Enter
  }
};
```

### Complex Key Combinations

```typescript
// Ctrl+Shift+I (Open Developer Tools)
const devToolsAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'I',
    options: {
      ctrlKey: true,
      shiftKey: true
    }
  }
};

// Ctrl+Alt+Delete
const ctrlAltDelAction = {
  type: 'simulateKeyboard',
  params: {
    key: 'Delete',
    options: {
      ctrlKey: true,
      altKey: true
    }
  }
};
```

## WebSocket Command Format

When using the WebSocket API, format your commands like this:

```json
{
  "type": "ELEMENT_QUERY",
  "id": "unique-id",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "selector": {
    "type": "css",
    "value": "#my-input"
  },
  "actions": [
    {
      "type": "simulateKeyboard",
      "params": {
        "key": "Enter",
        "options": {
          "ctrlKey": false,
          "altKey": false,
          "shiftKey": false,
          "metaKey": false
        }
      }
    }
  ]
}
```

## Supported Keys

### Regular Keys
- All letters: `a-z`, `A-Z`
- All numbers: `0-9`
- Special characters: `!@#$%^&*()_+-=[]{}|;:'",./<>?`

### Special Keys
- `Enter`, `Tab`, `Escape`, `Space`
- `Backspace`, `Delete`
- `Home`, `End`, `PageUp`, `PageDown`
- `ArrowUp`, `ArrowDown`, `ArrowLeft`, `ArrowRight`

### Function Keys
- `F1` through `F12`

### Modifier Keys
- `ctrlKey`: Control key
- `altKey`: Alt key
- `shiftKey`: Shift key
- `metaKey`: Command key (Mac) / Windows key

## Best Practices

1. **Focus the element first**: The `simulateKeyboard` action automatically focuses the target element.

2. **Use appropriate delays**: Add delays between rapid key presses to simulate realistic user behavior.

3. **Handle special keys properly**: Some keys like `Enter` and `Tab` have special behaviors that are automatically handled.

4. **Test thoroughly**: Different websites may handle keyboard events differently, so test your automation thoroughly.

5. **Combine with other actions**: Use `simulateKeyboard` in combination with `click`, `input`, and other actions for complex workflows.
