/**
 * simulateKeyboard 功能测试文件
 * 
 * 用于验证键盘模拟功能的正确性
 */

import { ElementActions } from '@/services/element-query/element-actions';
import type { ElementAction } from '@/types/element-query';

/**
 * 创建测试用的输入元素
 */
function createTestInput(): HTMLInputElement {
  const input = document.createElement('input');
  input.type = 'text';
  input.id = 'test-input';
  input.placeholder = 'Test input for keyboard simulation';
  document.body.appendChild(input);
  return input;
}

/**
 * 创建测试用的文本域元素
 */
function createTestTextarea(): HTMLTextAreaElement {
  const textarea = document.createElement('textarea');
  textarea.id = 'test-textarea';
  textarea.placeholder = 'Test textarea for keyboard simulation';
  document.body.appendChild(textarea);
  return textarea;
}

/**
 * 清理测试元素
 */
function cleanupTestElements() {
  const testInput = document.getElementById('test-input');
  const testTextarea = document.getElementById('test-textarea');
  if (testInput) testInput.remove();
  if (testTextarea) testTextarea.remove();
}

/**
 * 测试基本键盘输入
 */
async function testBasicKeyboardInput() {
  console.log('🧪 测试基本键盘输入...');
  
  const actions = new ElementActions();
  const input = createTestInput();
  
  try {
    // 测试字母键
    const letterAction: ElementAction = {
      type: 'simulateKeyboard',
      params: {
        key: 'a'
      }
    };
    
    const result = await actions.executeAction(input, letterAction);
    console.assert(result === true, '字母键输入应该成功');
    
    // 测试数字键
    const numberAction: ElementAction = {
      type: 'simulateKeyboard',
      params: {
        key: '1'
      }
    };
    
    const numberResult = await actions.executeAction(input, numberAction);
    console.assert(numberResult === true, '数字键输入应该成功');
    
    console.log('✅ 基本键盘输入测试通过');
  } catch (error) {
    console.error('❌ 基本键盘输入测试失败:', error);
  } finally {
    input.remove();
  }
}

/**
 * 测试特殊键输入
 */
async function testSpecialKeys() {
  console.log('🧪 测试特殊键输入...');
  
  const actions = new ElementActions();
  const input = createTestInput();
  
  try {
    // 测试 Enter 键
    const enterAction: ElementAction = {
      type: 'simulateKeyboard',
      params: {
        key: 'Enter'
      }
    };
    
    const enterResult = await actions.executeAction(input, enterAction);
    console.assert(enterResult === true, 'Enter键输入应该成功');
    
    // 测试 Tab 键
    const tabAction: ElementAction = {
      type: 'simulateKeyboard',
      params: {
        key: 'Tab'
      }
    };
    
    const tabResult = await actions.executeAction(input, tabAction);
    console.assert(tabResult === true, 'Tab键输入应该成功');
    
    // 测试 Escape 键
    const escapeAction: ElementAction = {
      type: 'simulateKeyboard',
      params: {
        key: 'Escape'
      }
    };
    
    const escapeResult = await actions.executeAction(input, escapeAction);
    console.assert(escapeResult === true, 'Escape键输入应该成功');
    
    console.log('✅ 特殊键输入测试通过');
  } catch (error) {
    console.error('❌ 特殊键输入测试失败:', error);
  } finally {
    input.remove();
  }
}

/**
 * 测试修饰键组合
 */
async function testModifierKeys() {
  console.log('🧪 测试修饰键组合...');
  
  const actions = new ElementActions();
  const input = createTestInput();
  
  try {
    // 测试 Ctrl+A (全选)
    const ctrlAAction: ElementAction = {
      type: 'simulateKeyboard',
      params: {
        key: 'a',
        options: {
          ctrlKey: true
        }
      }
    };
    
    const ctrlAResult = await actions.executeAction(input, ctrlAAction);
    console.assert(ctrlAResult === true, 'Ctrl+A组合键应该成功');
    
    // 测试 Shift+Tab
    const shiftTabAction: ElementAction = {
      type: 'simulateKeyboard',
      params: {
        key: 'Tab',
        options: {
          shiftKey: true
        }
      }
    };
    
    const shiftTabResult = await actions.executeAction(input, shiftTabAction);
    console.assert(shiftTabResult === true, 'Shift+Tab组合键应该成功');
    
    console.log('✅ 修饰键组合测试通过');
  } catch (error) {
    console.error('❌ 修饰键组合测试失败:', error);
  } finally {
    input.remove();
  }
}

/**
 * 测试箭头键
 */
async function testArrowKeys() {
  console.log('🧪 测试箭头键...');
  
  const actions = new ElementActions();
  const input = createTestInput();
  
  try {
    const arrowKeys = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
    
    for (const key of arrowKeys) {
      const arrowAction: ElementAction = {
        type: 'simulateKeyboard',
        params: {
          key: key
        }
      };
      
      const result = await actions.executeAction(input, arrowAction);
      console.assert(result === true, `${key}键输入应该成功`);
    }
    
    console.log('✅ 箭头键测试通过');
  } catch (error) {
    console.error('❌ 箭头键测试失败:', error);
  } finally {
    input.remove();
  }
}

/**
 * 测试功能键
 */
async function testFunctionKeys() {
  console.log('🧪 测试功能键...');
  
  const actions = new ElementActions();
  const input = createTestInput();
  
  try {
    const functionKeys = ['F1', 'F2', 'F5', 'F12'];
    
    for (const key of functionKeys) {
      const functionAction: ElementAction = {
        type: 'simulateKeyboard',
        params: {
          key: key
        }
      };
      
      const result = await actions.executeAction(input, functionAction);
      console.assert(result === true, `${key}键输入应该成功`);
    }
    
    console.log('✅ 功能键测试通过');
  } catch (error) {
    console.error('❌ 功能键测试失败:', error);
  } finally {
    input.remove();
  }
}

/**
 * 测试延迟参数
 */
async function testDelayParameter() {
  console.log('🧪 测试延迟参数...');
  
  const actions = new ElementActions();
  const input = createTestInput();
  
  try {
    const startTime = Date.now();
    
    const delayAction: ElementAction = {
      type: 'simulateKeyboard',
      params: {
        key: 'a',
        delay: 200
      }
    };
    
    const result = await actions.executeAction(input, delayAction);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.assert(result === true, '带延迟的键盘输入应该成功');
    console.assert(duration >= 200, '延迟时间应该正确');
    
    console.log('✅ 延迟参数测试通过');
  } catch (error) {
    console.error('❌ 延迟参数测试失败:', error);
  } finally {
    input.remove();
  }
}

/**
 * 运行所有 simulateKeyboard 测试
 */
export async function runSimulateKeyboardTests() {
  console.log('🚀 开始运行 simulateKeyboard 测试...');
  
  try {
    await testBasicKeyboardInput();
    await testSpecialKeys();
    await testModifierKeys();
    await testArrowKeys();
    await testFunctionKeys();
    await testDelayParameter();
    
    console.log('🎉 所有 simulateKeyboard 测试通过！');
  } catch (error) {
    console.error('❌ simulateKeyboard 测试失败:', error);
  } finally {
    cleanupTestElements();
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  runSimulateKeyboardTests();
}
