/**
 * 元素操作器
 * 执行各种元素操作，如点击、输入、滚动等
 */

import type { ElementAction } from '@/types/element-query';
import { ElementExtractor } from './element-extractor';

/**
 * 元素操作器类
 */
export class ElementActions {
  private extractor: ElementExtractor;

  constructor() {
    this.extractor = new ElementExtractor();
  }

  /**
   * 执行元素操作
   */
  async executeAction(element: Element, action: ElementAction): Promise<any> {
    const { type, params = {} } = action;
    const { delay = 0 } = params;

    // 执行前延迟
    if (delay > 0) {
      await this.sleep(delay);
    }

    switch (type) {
      case 'click':
        return this.clickElement(element, params.x, params.y);
      case 'input':
        return this.inputText(element, params.text || '');
      case 'scroll':
        return this.scrollElement(element, params.scrollX, params.scrollY);
      case 'hover':
        return this.hoverElement(element);
      case 'focus':
        return this.focusElement(element);
      case 'blur':
        return this.blurElement(element);
      case 'getAttribute':
        return this.getAttribute(element, params.attribute || '');
      case 'setText':
        return this.setText(element, params.text || '');
      case 'simulateKeyboard':
        return this.simulateKeyboard(element, params.key || '', params.options || {});
      default:
        throw new Error(`不支持的操作类型: ${type}`);
    }
  }

  /**
   * 点击元素
   */
  private async clickElement(element: Element, x?: number, y?: number): Promise<boolean> {
    try {
      // 添加空值检查
      if (!element) {
        console.error('Cannot click: element is null or undefined');
        return false;
      }

      // 确保元素可见
      this.scrollIntoView(element);
      await this.sleep(100);

      let clickX: number, clickY: number;

      if (x !== undefined && y !== undefined) {
        // 使用指定坐标（相对于元素）
        const rect = element.getBoundingClientRect();
        clickX = rect.left + x;
        clickY = rect.top + y;
      } else {
        // 使用元素中心点
        const center = this.extractor.getElementCenter(element);
        clickX = center.x;
        clickY = center.y;
      }

      // 创建点击事件
      const clickEvent = new MouseEvent('click', {
        view: window,
        bubbles: true,
        cancelable: true,
        clientX: clickX,
        clientY: clickY
      });

      // 触发事件
      element.dispatchEvent(clickEvent);

      // 如果是可点击元素，也调用原生点击
      if (element instanceof HTMLElement) {
        element.click();
      }

      return true;
    } catch (error) {
      console.error('点击元素失败:', error);
      return false;
    }
  }

  /**
   * 输入文本
   */
  private async inputText(element: Element, text: string): Promise<boolean> {
    try {
      // 检查元素是否支持文本输入
      if (!this.isElementEditable(element)) {
        throw new Error('元素不支持文本输入');
      }

      // 聚焦元素
      if (element instanceof HTMLElement) {
        element.focus();
        await this.sleep(50);
      }

      // 处理不同类型的可编辑元素
      if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
        // 处理 input 和 textarea 元素
        return this.inputToFormElement(element, text);
      } else if (this.isContentEditable(element)) {
        // 处理 contenteditable 元素
        return this.inputToContentEditable(element as HTMLElement, text);
      }

      return false;
    } catch (error) {
      console.error('输入文本失败:', error);
      return false;
    }
  }

  /**
   * 检查元素是否可编辑
   */
  private isElementEditable(element: Element): boolean {
    // 检查是否是表单输入元素
    if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
      return !element.disabled && !element.readOnly;
    }

    // 检查是否是 contenteditable 元素
    if (this.isContentEditable(element)) {
      return true;
    }

    return false;
  }

  /**
   * 检查元素是否是 contenteditable
   */
  private isContentEditable(element: Element): boolean {
    if (!(element instanceof HTMLElement)) {
      return false;
    }

    // 检查 contenteditable 属性
    const contentEditable = element.getAttribute('contenteditable');
    if (contentEditable === 'true' || contentEditable === '') {
      return true;
    }

    // 检查继承的 contenteditable
    return element.isContentEditable;
  }

  /**
   * 向表单元素输入文本
   */
  private async inputToFormElement(element: HTMLInputElement | HTMLTextAreaElement, text: string): Promise<boolean> {
    try {
      // 清空现有内容
      element.select();

      // 设置值
      element.value = text;

      // 触发输入事件
      const inputEvent = new Event('input', { bubbles: true });
      element.dispatchEvent(inputEvent);

      const changeEvent = new Event('change', { bubbles: true });
      element.dispatchEvent(changeEvent);

      return true;
    } catch (error) {
      console.error('向表单元素输入文本失败:', error);
      return false;
    }
  }

  /**
   * 向 contenteditable 元素输入文本
   */
  private async inputToContentEditable(element: HTMLElement, text: string): Promise<boolean> {
    try {
      // 选择所有内容
      const selection = window.getSelection();
      if (selection) {
        const range = document.createRange();
        range.selectNodeContents(element);
        selection.removeAllRanges();
        selection.addRange(range);
      }

      // 清空现有内容
      element.innerHTML = '';

      // 设置新文本
      element.textContent = text;

      // 触发输入事件
      const inputEvent = new Event('input', { bubbles: true });
      element.dispatchEvent(inputEvent);

      const changeEvent = new Event('change', { bubbles: true });
      element.dispatchEvent(changeEvent);

      // 触发 beforeinput 事件（某些应用可能需要）
      const beforeInputEvent = new InputEvent('beforeinput', {
        bubbles: true,
        cancelable: true,
        inputType: 'insertText',
        data: text
      });
      element.dispatchEvent(beforeInputEvent);

      return true;
    } catch (error) {
      console.error('向contenteditable元素输入文本失败:', error);
      return false;
    }
  }

  /**
   * 滚动元素
   */
  private async scrollElement(element: Element, scrollX?: number, scrollY?: number): Promise<boolean> {
    try {
      if (scrollX !== undefined || scrollY !== undefined) {
        // 滚动到指定位置
        element.scrollTo({
          left: scrollX || 0,
          top: scrollY || 0,
          behavior: 'smooth'
        });
      } else {
        // 滚动到元素可见
        this.scrollIntoView(element);
      }

      return true;
    } catch (error) {
      console.error('滚动元素失败:', error);
      return false;
    }
  }

  /**
   * 悬停元素
   */
  private async hoverElement(element: Element): Promise<boolean> {
    try {
      // 添加空值检查
      if (!element) {
        console.error('Cannot hover: element is null or undefined');
        return false;
      }

      const center = this.extractor.getElementCenter(element);

      const mouseEnterEvent = new MouseEvent('mouseenter', {
        view: window,
        bubbles: true,
        cancelable: true,
        clientX: center.x,
        clientY: center.y
      });

      const mouseOverEvent = new MouseEvent('mouseover', {
        view: window,
        bubbles: true,
        cancelable: true,
        clientX: center.x,
        clientY: center.y
      });

      element.dispatchEvent(mouseEnterEvent);
      element.dispatchEvent(mouseOverEvent);

      return true;
    } catch (error) {
      console.error('悬停元素失败:', error);
      return false;
    }
  }

  /**
   * 聚焦元素
   */
  private async focusElement(element: Element): Promise<boolean> {
    try {
      if (element instanceof HTMLElement) {
        element.focus();
        
        const focusEvent = new FocusEvent('focus', { bubbles: true });
        element.dispatchEvent(focusEvent);
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('聚焦元素失败:', error);
      return false;
    }
  }

  /**
   * 失焦元素
   */
  private async blurElement(element: Element): Promise<boolean> {
    try {
      if (element instanceof HTMLElement) {
        element.blur();
        
        const blurEvent = new FocusEvent('blur', { bubbles: true });
        element.dispatchEvent(blurEvent);
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('失焦元素失败:', error);
      return false;
    }
  }

  /**
   * 获取元素属性
   */
  private async getAttribute(element: Element, attributeName: string): Promise<string | null> {
    try {
      return element.getAttribute(attributeName);
    } catch (error) {
      console.error('获取属性失败:', error);
      return null;
    }
  }

  /**
   * 设置元素文本
   */
  private async setText(element: Element, text: string): Promise<boolean> {
    try {
      if (element instanceof HTMLElement) {
        element.textContent = text;
        
        // 触发变化事件
        const changeEvent = new Event('change', { bubbles: true });
        element.dispatchEvent(changeEvent);
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('设置文本失败:', error);
      return false;
    }
  }

  /**
   * 滚动元素到可见区域
   */
  private scrollIntoView(element: Element): void {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'center'
    });
  }

  /**
   * 等待指定时间
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 模拟键盘输入
   */
  async simulateKeyboard(element: Element, key: string, options: KeyboardEventInit = {}): Promise<boolean> {
    try {
      if (!(element instanceof HTMLElement)) {
        return false;
      }

      element.focus();

      const keydownEvent = new KeyboardEvent('keydown', {
        key,
        bubbles: true,
        cancelable: true,
        ...options
      });

      const keyupEvent = new KeyboardEvent('keyup', {
        key,
        bubbles: true,
        cancelable: true,
        ...options
      });

      element.dispatchEvent(keydownEvent);
      await this.sleep(50);
      element.dispatchEvent(keyupEvent);

      return true;
    } catch (error) {
      console.error('模拟键盘输入失败:', error);
      return false;
    }
  }

  /**
   * 拖拽元素
   */
  async dragElement(
    sourceElement: Element, 
    targetElement: Element, 
    options: { duration?: number } = {}
  ): Promise<boolean> {
    try {
      const { duration = 1000 } = options;
      
      const sourceCenter = this.extractor.getElementCenter(sourceElement);
      const targetCenter = this.extractor.getElementCenter(targetElement);

      // 开始拖拽
      const dragStartEvent = new DragEvent('dragstart', {
        bubbles: true,
        cancelable: true,
        clientX: sourceCenter.x,
        clientY: sourceCenter.y
      });

      sourceElement.dispatchEvent(dragStartEvent);

      // 模拟拖拽过程
      await this.sleep(duration / 2);

      // 结束拖拽
      const dropEvent = new DragEvent('drop', {
        bubbles: true,
        cancelable: true,
        clientX: targetCenter.x,
        clientY: targetCenter.y
      });

      targetElement.dispatchEvent(dropEvent);

      const dragEndEvent = new DragEvent('dragend', {
        bubbles: true,
        cancelable: true,
        clientX: targetCenter.x,
        clientY: targetCenter.y
      });

      sourceElement.dispatchEvent(dragEndEvent);

      return true;
    } catch (error) {
      console.error('拖拽元素失败:', error);
      return false;
    }
  }

  /**
   * 等待元素状态改变
   */
  async waitForElementState(
    element: Element, 
    condition: (el: Element) => boolean, 
    timeout: number = 5000
  ): Promise<boolean> {
    const startTime = Date.now();

    return new Promise((resolve) => {
      const checkCondition = () => {
        if (condition(element)) {
          resolve(true);
        } else if (Date.now() - startTime >= timeout) {
          resolve(false);
        } else {
          setTimeout(checkCondition, 100);
        }
      };

      checkCondition();
    });
  }

  /**
   * 获取元素的所有事件监听器（调试用）
   */
  getEventListeners(element: Element): string[] {
    const events: string[] = [];
    
    // 常见事件类型
    const eventTypes = [
      'click', 'dblclick', 'mousedown', 'mouseup', 'mouseover', 'mouseout',
      'keydown', 'keyup', 'keypress', 'focus', 'blur', 'change', 'input',
      'submit', 'load', 'resize', 'scroll', 'touchstart', 'touchend'
    ];

    eventTypes.forEach(eventType => {
      // 检查是否有对应的属性
      const handlerProperty = `on${eventType}` as keyof Element;
      if ((element as any)[handlerProperty]) {
        events.push(eventType);
      }
    });

    return events;
  }
}
