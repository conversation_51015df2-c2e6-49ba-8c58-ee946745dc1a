/**
 * 元素查询相关类型定义
 */

// 支持的选择器类型
export type SelectorType = 'xpath' | 'css' | 'id' | 'class' | 'tag' | 'text' | 'attribute';

// 元素查询指令
export interface ElementQueryCommand {
  type: 'ELEMENT_QUERY';
  id: string;
  timestamp: string;
  selector: {
    type: SelectorType;
    value: string;
    options?: {
      multiple?: boolean;  // 是否查找多个元素
      timeout?: number;    // 查找超时时间（毫秒）
      waitVisible?: boolean; // 是否等待元素可见
      index?: number;      // 当multiple为true时，返回指定索引的元素
    };
  };
  actions?: ElementAction[]; // 可选的元素操作
}

// 元素操作类型
export type ElementActionType = 'click' | 'input' | 'scroll' | 'hover' | 'focus' | 'blur' | 'getAttribute' | 'setText';

// 元素操作定义
export interface ElementAction {
  type: ElementActionType;
  params?: {
    text?: string;        // 用于input和setText
    x?: number;          // 用于click的相对坐标
    y?: number;          // 用于click的相对坐标
    attribute?: string;   // 用于getAttribute
    scrollX?: number;     // 用于scroll
    scrollY?: number;     // 用于scroll
    delay?: number;       // 操作延迟（毫秒）
    key?: string;        // 用于simulateKeyboard
    options?: {
      ctrlKey?: boolean;
      altKey?: boolean;
      shiftKey?: boolean;
      metaKey?: boolean;
    };
  };
}

// 元素位置信息
export interface ElementPosition {
  x: number;           // 相对于视口的X坐标
  y: number;           // 相对于视口的Y坐标
  pageX: number;       // 相对于页面的X坐标
  pageY: number;       // 相对于页面的Y坐标
  width: number;       // 元素宽度
  height: number;      // 元素高度
  top: number;         // 距离视口顶部距离
  left: number;        // 距离视口左侧距离
  right: number;       // 距离视口右侧距离
  bottom: number;      // 距离视口底部距离
}

// 元素样式信息
export interface ElementStyles {
  display: string;
  visibility: string;
  opacity: string;
  zIndex: string;
  position: string;
  backgroundColor: string;
  color: string;
  fontSize: string;
  fontFamily: string;
  border: string;
  margin: string;
  padding: string;
}

// 元素属性信息
export interface ElementAttributes {
  id?: string;
  className?: string;
  tagName: string;
  textContent?: string;
  innerHTML?: string;
  value?: string;
  href?: string;
  src?: string;
  alt?: string;
  title?: string;
  placeholder?: string;
  disabled?: boolean;
  checked?: boolean;
  selected?: boolean;
  [key: string]: any; // 其他自定义属性
}

// 元素详细信息
export interface ElementInfo {
  selector: string;        // 用于查找的选择器
  selectorType: SelectorType; // 选择器类型
  position: ElementPosition;   // 位置信息
  styles: ElementStyles;       // 样式信息
  attributes: ElementAttributes; // 属性信息
  isVisible: boolean;         // 是否可见
  isInViewport: boolean;      // 是否在视口内
  hasChildren: boolean;       // 是否有子元素
  childrenCount: number;      // 子元素数量
  parentSelector?: string;    // 父元素选择器
  xpath?: string;            // 元素的XPath
  cssPath?: string;          // 元素的CSS路径
}

// 元素查询结果
export interface ElementQueryResult {
  type: 'ELEMENT_QUERY_RESULT';
  id: string; // 对应查询指令的ID
  timestamp: string;
  success: boolean;
  elements: ElementInfo[]; // 找到的元素列表
  count: number;          // 找到的元素数量
  error?: string;         // 错误信息
  executionTime: number;  // 执行时间（毫秒）
}

// 元素操作结果
export interface ElementActionResult {
  type: 'ELEMENT_ACTION_RESULT';
  id: string; // 对应查询指令的ID
  timestamp: string;
  success: boolean;
  actionType: ElementActionType;
  result?: any;          // 操作结果（如getAttribute的返回值）
  error?: string;        // 错误信息
  executionTime: number; // 执行时间（毫秒）
}

// 页面信息查询指令
export interface PageInfoCommand {
  type: 'PAGE_INFO';
  id: string;
  timestamp: string;
  info: ('url' | 'title' | 'size' | 'scroll' | 'viewport' | 'all')[];
}

// 页面信息结果
export interface PageInfoResult {
  type: 'PAGE_INFO_RESULT';
  id: string;
  timestamp: string;
  success: boolean;
  url?: string;
  title?: string;
  size?: {
    width: number;
    height: number;
    scrollWidth: number;
    scrollHeight: number;
  } | null;
  scroll?: {
    x: number;
    y: number;
  } | null;
  viewport?: {
    width: number;
    height: number;
  } | null;
}

// 元素监听指令
export interface ElementWatchCommand {
  type: 'ELEMENT_WATCH';
  id: string;
  timestamp: string;
  selector: {
    type: SelectorType;
    value: string;
  };
  events: string[]; // 要监听的事件类型
  options?: {
    once?: boolean;     // 是否只监听一次
    timeout?: number;   // 监听超时时间
  };
}

// 元素事件结果
export interface ElementEventResult {
  type: 'ELEMENT_EVENT';
  id: string; // 对应监听指令的ID
  timestamp: string;
  eventType: string;
  element: ElementInfo;
  eventData?: any; // 事件相关数据
}

// 批量元素查询指令
export interface BatchElementQueryCommand {
  type: 'BATCH_ELEMENT_QUERY';
  id: string;
  timestamp: string;
  queries: Array<{
    id: string;
    selector: ElementQueryCommand['selector'];
    actions?: ElementAction[];
  }>;
}

// 批量查询结果
export interface BatchElementQueryResult {
  type: 'BATCH_ELEMENT_QUERY_RESULT';
  id: string;
  timestamp: string;
  results: Array<{
    id: string;
    success: boolean;
    elements: ElementInfo[];
    error?: string;
  }>;
  totalExecutionTime: number;
}

// 元素截图指令
export interface ElementScreenshotCommand {
  type: 'ELEMENT_SCREENSHOT';
  id: string;
  timestamp: string;
  selector: {
    type: SelectorType;
    value: string;
  };
  options?: {
    format?: 'png' | 'jpeg' | 'webp';
    quality?: number; // 0-1，仅对jpeg有效
    padding?: number; // 截图边距
  };
}

// 元素截图结果
export interface ElementScreenshotResult {
  type: 'ELEMENT_SCREENSHOT_RESULT';
  id: string;
  timestamp: string;
  success: boolean;
  dataUrl?: string; // base64编码的图片数据
  error?: string;
  element?: ElementInfo;
}

// 联合类型：所有支持的指令
export type ElementCommand = 
  | ElementQueryCommand 
  | PageInfoCommand 
  | ElementWatchCommand 
  | BatchElementQueryCommand
  | ElementScreenshotCommand;

// 联合类型：所有结果类型
export type ElementResult = 
  | ElementQueryResult 
  | ElementActionResult 
  | PageInfoResult 
  | ElementEventResult 
  | BatchElementQueryResult
  | ElementScreenshotResult;

// 选择器验证函数类型
export type SelectorValidator = (selector: string) => boolean;

// 元素查找选项
export interface ElementFindOptions {
  timeout?: number;
  waitVisible?: boolean;
  multiple?: boolean;
  index?: number;
  retryInterval?: number;
  maxRetries?: number;
}
